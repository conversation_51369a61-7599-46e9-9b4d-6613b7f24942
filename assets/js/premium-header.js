/**
 * Premium Header JavaScript for Nội Thất Bàng Vũ
 * Handles interactive features of the premium header
 */

document.addEventListener('DOMContentLoaded', function () {
  // Tablet Navigation Touch Scroll
  const navMenu = document.querySelector('.nav-menu');
  if (navMenu) {
    let isDown = false;
    let startX;
    let scrollLeft;

    // Mouse events
    navMenu.addEventListener('mousedown', (e) => {
      isDown = true;
      startX = e.pageX - navMenu.offsetLeft;
      scrollLeft = navMenu.scrollLeft;
      navMenu.style.cursor = 'grabbing';
    });

    navMenu.addEventListener('mouseleave', () => {
      isDown = false;
      navMenu.style.cursor = 'grab';
    });

    navMenu.addEventListener('mouseup', () => {
      isDown = false;
      navMenu.style.cursor = 'grab';
    });

    navMenu.addEventListener('mousemove', (e) => {
      if (!isDown) return;
      e.preventDefault();
      const x = e.pageX - navMenu.offsetLeft;
      const walk = (x - startX) * 2;
      navMenu.scrollLeft = scrollLeft - walk;
    });

    // Touch events for mobile/tablet
    navMenu.addEventListener('touchstart', (e) => {
      startX = e.touches[0].pageX - navMenu.offsetLeft;
      scrollLeft = navMenu.scrollLeft;
    });

    navMenu.addEventListener('touchmove', (e) => {
      const x = e.touches[0].pageX - navMenu.offsetLeft;
      const walk = (x - startX) * 2;
      navMenu.scrollLeft = scrollLeft - walk;
    });
  }

  // Mobile menu toggle
  const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
  const mobileMenu = document.querySelector('.mobile-menu');
  const mobileMenuClose = document.querySelector('.mobile-menu-close');
  const mobileMenuOverlay = document.querySelector('.mobile-menu-overlay');

  if (mobileMenuToggle && mobileMenu) {
    mobileMenuToggle.addEventListener('click', function () {
      mobileMenu.classList.add('active');
      document.body.classList.add('overflow-hidden');
      if (mobileMenuOverlay) {
        mobileMenuOverlay.classList.add('active');
      }
    });
  }

  if (mobileMenuClose && mobileMenu) {
    mobileMenuClose.addEventListener('click', function () {
      mobileMenu.classList.remove('active');
      document.body.classList.remove('overflow-hidden');
      if (mobileMenuOverlay) {
        mobileMenuOverlay.classList.remove('active');
      }
    });
  }

  if (mobileMenuOverlay) {
    mobileMenuOverlay.addEventListener('click', function () {
      mobileMenu.classList.remove('active');
      document.body.classList.remove('overflow-hidden');
      this.classList.remove('active');
    });
  }

  // Mobile dropdown toggles
  const mobileDropdownToggles = document.querySelectorAll(
    '.mobile-dropdown-toggle'
  );

  mobileDropdownToggles.forEach(function (toggle) {
    toggle.addEventListener('click', function (e) {
      e.preventDefault();
      const parent = this.parentElement;
      const submenu = parent.querySelector('.mobile-submenu');

      if (parent.classList.contains('active')) {
        parent.classList.remove('active');
        submenu.style.maxHeight = '0px';
        this.querySelector('i').classList.remove('fa-chevron-up');
        this.querySelector('i').classList.add('fa-chevron-down');
      } else {
        parent.classList.add('active');
        submenu.style.maxHeight = submenu.scrollHeight + 'px';
        this.querySelector('i').classList.remove('fa-chevron-down');
        this.querySelector('i').classList.add('fa-chevron-up');
      }
    });
  });

  // New header scroll behavior
  const header = document.querySelector('.premium-header');
  const topBar = document.querySelector('.top-bar');
  const logoImage = document.querySelector('.premium-logo-image img');
  const originalLogoSrc = logoImage ? logoImage.src : '';
  const darkLogoSrc = BASE_URL + '/assets/images/logo/logo-chu-trang.svg';

  let headerHeight = 0;
  let isCompactMode = false;
  let ticking = false;
  const compactTriggerOffset = 30; // 30px thêm sau khi header biến mất

  if (header) {
    // Tính toán chiều cao header ban đầu
    headerHeight = header.offsetHeight;

    // Thêm class để kích hoạt hiệu ứng transition mượt mà
    header.classList.add('smooth-transition');

    // Logic scroll mới
    window.addEventListener('scroll', function () {
      if (!ticking) {
        window.requestAnimationFrame(function () {
          const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

          // Kiểm tra xem có phải mobile không
          const isMobile = window.innerWidth <= 767;

          // Logic mới (chỉ áp dụng trên desktop):
          // 1. Cuộn bình thường: Header cuộn theo trang (không sticky)
          // 2. Khi cuộn hết header: Header biến mất hoàn toàn
          // 3. Cuộn thêm 30px: Hiển thị compact header (Tier 2 + Tier 3)
          // 4. Cuộn về đầu trang: Hiển thị lại toàn bộ header

          if (isMobile) {
            // Trên mobile, giữ nguyên behavior cũ
            header.classList.remove('compact-mode');
            isCompactMode = false;
            if (logoImage) logoImage.src = originalLogoSrc;
          } else {
            // Logic cho desktop
            if (scrollTop === 0) {
              // Ở đầu trang - hiển thị toàn bộ header, ẩn compact mode
              header.classList.remove('compact-mode');
              isCompactMode = false;
              if (logoImage) logoImage.src = originalLogoSrc;
            } else if (scrollTop > headerHeight + compactTriggerOffset) {
              // Cuộn qua header + 30px - hiển thị compact mode
              if (!isCompactMode) {
                header.classList.add('compact-mode');
                isCompactMode = true;
                if (logoImage) logoImage.src = darkLogoSrc;
              }
            } else if (scrollTop <= headerHeight && isCompactMode) {
              // Cuộn ngược lại trong vùng header - ẩn compact mode
              header.classList.remove('compact-mode');
              isCompactMode = false;
              if (logoImage) logoImage.src = originalLogoSrc;
            }
          }

          ticking = false;
        });
        ticking = true;
      }
    });

    // Kiểm tra trạng thái ban đầu khi trang tải
    const initialScrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const isMobile = window.innerWidth <= 767;

    if (!isMobile && initialScrollTop > headerHeight + compactTriggerOffset) {
      header.classList.add('compact-mode');
      isCompactMode = true;
      if (logoImage) logoImage.src = darkLogoSrc;
    }

    // Xử lý resize window
    window.addEventListener('resize', function() {
      const newIsMobile = window.innerWidth <= 767;
      if (newIsMobile && isCompactMode) {
        header.classList.remove('compact-mode');
        isCompactMode = false;
        if (logoImage) logoImage.src = originalLogoSrc;
      }
      // Cập nhật lại chiều cao header khi resize
      headerHeight = header.offsetHeight;
    });
  }

  // Search input focus effect
  const searchInput = document.querySelector('.search-input');
  const searchForm = document.querySelector('.search-form');

  if (searchInput && searchForm) {
    searchInput.addEventListener('focus', function () {
      searchForm.classList.add('focused');

      // Thêm hiệu ứng ripple khi focus
      const ripple = document.createElement('span');
      ripple.classList.add('search-ripple');
      searchForm.appendChild(ripple);

      // Xóa ripple sau khi hiệu ứng hoàn thành
      setTimeout(() => {
        ripple.remove();
      }, 600);
    });

    searchInput.addEventListener('blur', function () {
      searchForm.classList.remove('focused');
    });
  }

  // Live search functionality - VÔ HIỆU HÓA để tránh xung đột với search-improved.js
  if (false && searchInput) {
    // Tạo container cho kết quả tìm kiếm nếu chưa có
    let searchSuggestions = document.querySelector('.search-suggestions');
    if (!searchSuggestions) {
      searchSuggestions = document.createElement('div');
      searchSuggestions.className = 'search-suggestions';
      searchForm.appendChild(searchSuggestions);
    }

    // Thêm CSS inline cho search-suggestions nếu chưa có
    searchSuggestions.style.position = 'absolute';
    searchSuggestions.style.left = '0';
    searchSuggestions.style.right = '0';
    searchSuggestions.style.top = 'calc(100% + 5px)';
    searchSuggestions.style.zIndex = '9999';
    searchSuggestions.style.border = '1px solid #e2e8f0';
    searchSuggestions.style.borderRadius = '0.375rem';
    searchSuggestions.style.backgroundColor = 'white';
    searchSuggestions.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.1)';
    searchSuggestions.style.maxHeight = '80vh';
    searchSuggestions.style.overflowY = 'auto';
    searchSuggestions.style.padding = '10px';
    searchSuggestions.style.display = 'none';

    // Xử lý sự kiện input
    searchInput.addEventListener('input', function () {
      const searchValue = this.value.trim();

      if (searchValue.length > 0) {
        // Tạo dữ liệu mẫu để hiển thị
        const sampleData = [
          {
            id: 1,
            name: 'Sofa da cao cấp đẳng cấp dân chơi',
            category: 'Phòng khách',
            price: '15.000.000 đ',
            image: BASE_URL + '/uploads/products/sample-product-1.jpg',
          },
          {
            id: 2,
            name: 'Sofa da cao cấp Milano',
            category: 'Phòng khách',
            price: '12.000.000 đ',
            image: BASE_URL + '/uploads/products/sample-product-2.jpg',
          },
          {
            id: 3,
            name: 'Sofa da cao cấp Milanoooooo',
            category: 'Phòng khách',
            price: '12.000.000 đ',
            image: BASE_URL + '/uploads/products/sample-product-3.jpg',
          },
          {
            id: 4,
            name: 'Tủ bếp hiện đại',
            category: 'Phòng bếp',
            price: '15.000.000 đ',
            image: BASE_URL + '/uploads/products/sample-product-4.jpg',
          },
        ];

        // Lọc dữ liệu mẫu theo từ khóa tìm kiếm
        const filteredData = sampleData.filter(
          (item) =>
            item.name.toLowerCase().includes(searchValue.toLowerCase()) ||
            item.category.toLowerCase().includes(searchValue.toLowerCase())
        );

        // Tạo HTML cho kết quả tìm kiếm
        let resultsHTML = '';

        if (filteredData.length > 0) {
          filteredData.forEach((item) => {
            resultsHTML += `
              <div class="search-item" onclick="window.location.href='${BASE_URL}/san-pham/${
              item.slug
            }'">
                <div class="search-item-image">
                  <div class="skeleton-loading skeleton-wave" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; border-radius: 0.5rem; z-index: 1;"></div>
                  <img
                    src="${item.image}"
                    alt="${item.name}"
                    style="width: 100%; height: 100%; object-fit: cover; border-radius: 0.5rem; position: relative; z-index: 2;"
                    onerror="this.style.display='none';"
                    onload="this.previousElementSibling.style.display='none';"
                  >
                </div>
                <div class="search-item-info">
                  <div class="search-item-name">${item.name}</div>
                  <div class="search-item-meta">
                    <div class="search-item-rating">
                      <i class="fas fa-star"></i>
                      <span>${item.rating || '0.0'}</span>
                    </div>
                    <div class="search-item-sales">
                      <span>${item.sales || '0'} đã bán</span>
                    </div>
                  </div>
                  <div class="search-item-category">${item.category}</div>
                </div>
                <div class="search-item-price">${item.price}</div>
              </div>
            `;
          });
        } else {
          resultsHTML = `
            <div class="search-no-results">
              <i class="fas fa-search" style="font-size: 1.5rem; margin-bottom: 0.5rem; color: #999;"></i>
              <p>Không tìm thấy sản phẩm nào phù hợp</p>
            </div>
          `;
        }

        // Cập nhật nội dung và hiển thị kết quả
        searchSuggestions.innerHTML = resultsHTML;
        searchSuggestions.style.display = 'block';
      } else {
        // Ẩn container kết quả nếu input quá ngắn
        searchSuggestions.style.display = 'none';
      }
    });

    // Xử lý sự kiện click bên ngoài để ẩn kết quả
    document.addEventListener('click', function (e) {
      if (!searchForm.contains(e.target)) {
        searchSuggestions.style.display = 'none';
      }
    });

    // Đảm bảo kết quả tìm kiếm hiển thị đúng khi focus vào input
    searchInput.addEventListener('focus', function () {
      if (this.value.trim().length > 0) {
        // Kích hoạt sự kiện input để hiển thị kết quả
        const event = new Event('input', { bubbles: true });
        this.dispatchEvent(event);
      }
    });
  }

  // Tablet touch-friendly dropdowns
  function initTabletDropdowns() {
    const userDropdownToggle = document.querySelector('.user-dropdown .action-btn');
    const userDropdownMenu = document.querySelector('.user-dropdown-menu');
    const cartBtn = document.querySelector('.cart-btn');
    const miniCart = document.querySelector('.mini-cart');



    // Kiểm tra xem có phải tablet không (768px - 1024px)
    function isTablet() {
      return window.innerWidth >= 768 && window.innerWidth <= 1024;
    }

    // Xử lý user dropdown
    if (userDropdownToggle && userDropdownMenu) {
      userDropdownToggle.addEventListener('click', function (e) {
        if (isTablet()) {
          e.preventDefault();
          e.stopPropagation(); // Ngăn event bubbling

          // Toggle dropdown
          const isActive = userDropdownMenu.classList.contains('active');
          const userDropdown = userDropdownToggle.closest('.user-dropdown');

          // Đóng tất cả dropdown khác và reset icon
          document.querySelectorAll('.user-dropdown, .cart-container').forEach(container => {
            container.classList.remove('has-active');
          });
          document.querySelectorAll('.user-dropdown-menu, .mini-cart').forEach(dropdown => {
            dropdown.classList.remove('active');
          });

          // Reset tất cả aria-expanded về false để icon quay xuống
          document.querySelectorAll('.user-dropdown .action-btn').forEach(btn => {
            btn.setAttribute('aria-expanded', 'false');
          });

          // Toggle dropdown hiện tại
          if (!isActive) {
            userDropdownMenu.classList.add('active');
            userDropdown.classList.add('has-active');
            userDropdownToggle.setAttribute('aria-expanded', 'true');
          } else {
            userDropdown.classList.remove('has-active');
            userDropdownToggle.setAttribute('aria-expanded', 'false');
          }
        }
      });
    }

    // Xử lý cart dropdown
    if (cartBtn && miniCart) {
      cartBtn.addEventListener('click', function (e) {
        if (isTablet()) {
          e.preventDefault();
          e.stopPropagation(); // Ngăn event bubbling

          // Toggle dropdown
          const isActive = miniCart.classList.contains('active');
          const cartContainer = cartBtn.closest('.cart-container');

          // Đóng tất cả dropdown khác và reset icon
          document.querySelectorAll('.user-dropdown, .cart-container').forEach(container => {
            container.classList.remove('has-active');
          });
          document.querySelectorAll('.user-dropdown-menu, .mini-cart').forEach(dropdown => {
            dropdown.classList.remove('active');
          });

          // Reset tất cả aria-expanded về false để icon quay xuống
          document.querySelectorAll('.user-dropdown .action-btn').forEach(btn => {
            btn.setAttribute('aria-expanded', 'false');
          });

          // Toggle dropdown hiện tại
          if (!isActive) {
            miniCart.classList.add('active');
            cartContainer.classList.add('has-active');
          } else {
            cartContainer.classList.remove('has-active');
          }
        }
      });
    }

    // Đóng dropdown khi click bên ngoài
    document.addEventListener('click', function (e) {
      if (isTablet()) {
        const isClickInsideDropdown = e.target.closest('.user-dropdown, .cart-container');

        if (!isClickInsideDropdown) {
          // Xóa tất cả active states
          document.querySelectorAll('.user-dropdown, .cart-container').forEach(container => {
            container.classList.remove('has-active');
          });
          document.querySelectorAll('.user-dropdown-menu, .mini-cart').forEach(dropdown => {
            dropdown.classList.remove('active');
          });

          // Reset tất cả aria-expanded để icon quay xuống
          document.querySelectorAll('.user-dropdown .action-btn').forEach(btn => {
            btn.setAttribute('aria-expanded', 'false');
          });
        }
      }
    });

    // Xử lý khi resize window
    window.addEventListener('resize', function() {
      if (!isTablet()) {
        // Nếu không phải tablet, xóa tất cả active class
        document.querySelectorAll('.user-dropdown, .cart-container').forEach(container => {
          container.classList.remove('has-active');
        });
        document.querySelectorAll('.user-dropdown-menu, .mini-cart').forEach(dropdown => {
          dropdown.classList.remove('active');
        });

        // Reset tất cả aria-expanded để icon quay xuống
        document.querySelectorAll('.user-dropdown .action-btn').forEach(btn => {
          btn.setAttribute('aria-expanded', 'false');
        });
      }
    });
  }

  // Khởi tạo tablet dropdowns
  initTabletDropdowns();
});
